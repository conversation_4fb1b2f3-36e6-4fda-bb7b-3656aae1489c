/**
 * أداة تشخيص وإصلاح حساب الأرباح في قاعدة البيانات
 * 
 * هذه الأداة تتحقق من صحة حساب الأرباح وتصلح أي مشاكل
 */

/**
 * تشخيص شامل لحساب الأرباح
 */
async function diagnoseProfitCalculation() {
  console.log('🔍 بدء تشخيص شامل لحساب الأرباح...');
  console.log('='.repeat(60));
  
  try {
    // 1. الحصول على بيانات الخزينة الحالية
    const cashboxQuery = 'SELECT * FROM cashbox WHERE id = 1 LIMIT 1';
    const cashboxResult = await window.api.invoke('execute-direct-query', { query: cashboxQuery });
    
    if (!cashboxResult.success || !cashboxResult.data || cashboxResult.data.length === 0) {
      console.error('❌ لا يمكن الحصول على بيانات الخزينة');
      return;
    }
    
    const cashbox = cashboxResult.data[0];
    console.log('📊 بيانات الخزينة الحالية:');
    console.log(`   - المبيعات: ${cashbox.sales_total || 0}`);
    console.log(`   - المشتريات: ${cashbox.purchases_total || 0}`);
    console.log(`   - مصاريف النقل: ${cashbox.transport_total || 0}`);
    console.log(`   - الأرباح المحفوظة: ${cashbox.profit_total || 0}`);
    
    // 2. حساب الأرباح الصحيحة من المعادلة البسيطة
    const simpleProfitCalculation = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
    console.log(`   - الأرباح المحسوبة (بسيط): ${simpleProfitCalculation}`);
    
    // 3. حساب الأرباح من معاملات البيع الفعلية
    const salesQuery = `
      SELECT 
        SUM(CASE WHEN transaction_type = 'sale' THEN profit ELSE 0 END) as total_sales_profit,
        SUM(CASE WHEN transaction_type = 'return' THEN -ABS(profit) ELSE 0 END) as total_returns_loss,
        COUNT(CASE WHEN transaction_type = 'sale' THEN 1 END) as sales_count,
        COUNT(CASE WHEN transaction_type = 'return' THEN 1 END) as returns_count
      FROM transactions 
      WHERE transaction_type IN ('sale', 'return')
    `;
    
    const salesResult = await window.api.invoke('execute-direct-query', { query: salesQuery });
    
    if (salesResult.success && salesResult.data && salesResult.data.length > 0) {
      const salesData = salesResult.data[0];
      const actualProfit = (salesData.total_sales_profit || 0) + (salesData.total_returns_loss || 0);
      
      console.log('\n📈 تحليل معاملات البيع:');
      console.log(`   - أرباح المبيعات: ${salesData.total_sales_profit || 0}`);
      console.log(`   - خسائر المرتجعات: ${salesData.total_returns_loss || 0}`);
      console.log(`   - صافي الأرباح الفعلية: ${actualProfit}`);
      console.log(`   - عدد المبيعات: ${salesData.sales_count || 0}`);
      console.log(`   - عدد المرتجعات: ${salesData.returns_count || 0}`);
      
      // 4. مقارنة النتائج
      console.log('\n🔍 مقارنة النتائج:');
      console.log(`   - الأرباح في الخزينة: ${cashbox.profit_total || 0}`);
      console.log(`   - الأرباح المحسوبة (بسيط): ${simpleProfitCalculation}`);
      console.log(`   - الأرباح الفعلية (من المعاملات): ${actualProfit}`);
      
      const differenceSimple = Math.abs((cashbox.profit_total || 0) - simpleProfitCalculation);
      const differenceActual = Math.abs((cashbox.profit_total || 0) - actualProfit);
      
      console.log(`   - الفرق مع الحساب البسيط: ${differenceSimple}`);
      console.log(`   - الفرق مع الأرباح الفعلية: ${differenceActual}`);
      
      // 5. تحديد المشكلة وتقديم الحل
      console.log('\n🎯 التشخيص:');
      
      if (differenceSimple > 0.01) {
        console.log('❌ مشكلة: الأرباح في الخزينة لا تطابق الحساب البسيط');
        console.log(`   المتوقع: ${simpleProfitCalculation}, الموجود: ${cashbox.profit_total}`);
      }
      
      if (differenceActual > 0.01) {
        console.log('❌ مشكلة: الأرباح في الخزينة لا تطابق الأرباح الفعلية من المعاملات');
        console.log(`   المتوقع: ${actualProfit}, الموجود: ${cashbox.profit_total}`);
      }
      
      if (Math.abs(simpleProfitCalculation - actualProfit) > 0.01) {
        console.log('⚠️ تحذير: هناك فرق بين الحساب البسيط والأرباح الفعلية');
        console.log('   هذا قد يعني أن هناك مشكلة في حساب الأرباح في المعاملات');
      }
      
      // 6. اقتراح الإصلاح
      console.log('\n🔧 اقتراح الإصلاح:');
      
      if (differenceActual <= 0.01) {
        console.log('✅ الأرباح الفعلية صحيحة، يجب استخدامها');
        console.log(`   القيمة الصحيحة: ${actualProfit}`);
        return {
          correct_profit: actualProfit,
          current_profit: cashbox.profit_total,
          needs_fix: differenceActual > 0.01,
          recommended_action: 'use_actual_profit'
        };
      } else if (differenceSimple <= 0.01) {
        console.log('✅ الحساب البسيط صحيح، يجب استخدامه');
        console.log(`   القيمة الصحيحة: ${simpleProfitCalculation}`);
        return {
          correct_profit: simpleProfitCalculation,
          current_profit: cashbox.profit_total,
          needs_fix: differenceSimple > 0.01,
          recommended_action: 'use_simple_calculation'
        };
      } else {
        console.log('❓ غير واضح، يحتاج فحص يدوي');
        return {
          correct_profit: null,
          current_profit: cashbox.profit_total,
          needs_fix: true,
          recommended_action: 'manual_review'
        };
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في تشخيص الأرباح:', error);
    return null;
  }
}

/**
 * إصلاح الأرباح في قاعدة البيانات
 */
async function fixProfitInDatabase(correctProfit) {
  console.log(`🔧 إصلاح الأرباح في قاعدة البيانات إلى: ${correctProfit}`);
  
  try {
    const updateQuery = `UPDATE cashbox SET profit_total = ? WHERE id = 1`;
    const result = await window.api.invoke('execute-direct-query', { 
      query: updateQuery,
      params: [correctProfit]
    });
    
    if (result.success) {
      console.log('✅ تم إصلاح الأرباح في قاعدة البيانات بنجاح');
      
      // إرسال حدث تحديث للواجهة
      if (window.dispatchEvent) {
        window.dispatchEvent(new CustomEvent('cashbox-updated-ui', {
          detail: {
            profit_total: correctProfit,
            updated_at: new Date().toISOString(),
            source: 'profit_fix'
          }
        }));
      }
      
      return true;
    } else {
      console.error('❌ فشل في إصلاح الأرباح:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ خطأ في إصلاح الأرباح:', error);
    return false;
  }
}

/**
 * تشغيل التشخيص والإصلاح التلقائي
 */
async function runProfitDiagnosisAndFix() {
  console.log('🚀 بدء التشخيص والإصلاح التلقائي للأرباح...');
  
  const diagnosis = await diagnoseProfitCalculation();
  
  if (!diagnosis) {
    console.log('❌ فشل في التشخيص');
    return;
  }
  
  if (diagnosis.needs_fix && diagnosis.correct_profit !== null) {
    console.log(`\n🔧 سيتم إصلاح الأرباح من ${diagnosis.current_profit} إلى ${diagnosis.correct_profit}`);
    
    const userConfirm = confirm(`هل تريد إصلاح الأرباح من ${diagnosis.current_profit} إلى ${diagnosis.correct_profit}؟`);
    
    if (userConfirm) {
      const fixed = await fixProfitInDatabase(diagnosis.correct_profit);
      
      if (fixed) {
        console.log('🎉 تم إصلاح الأرباح بنجاح!');
        
        // إعادة تحميل صفحة الخزينة إذا كانت مفتوحة
        if (window.location.hash.includes('#/cashbox') && window.loadCashbox) {
          setTimeout(() => {
            window.loadCashbox();
          }, 1000);
        }
      }
    } else {
      console.log('❌ تم إلغاء الإصلاح بواسطة المستخدم');
    }
  } else if (!diagnosis.needs_fix) {
    console.log('✅ الأرباح صحيحة، لا تحتاج إصلاح');
  } else {
    console.log('❓ يحتاج فحص يدوي، لا يمكن الإصلاح التلقائي');
  }
  
  return diagnosis;
}

/**
 * عرض تفاصيل الأرباح الحالية
 */
async function showCurrentProfitDetails() {
  console.log('📊 عرض تفاصيل الأرباح الحالية...');
  
  try {
    // بيانات الخزينة
    const cashboxQuery = 'SELECT * FROM cashbox WHERE id = 1 LIMIT 1';
    const cashboxResult = await window.api.invoke('execute-direct-query', { query: cashboxQuery });
    
    if (cashboxResult.success && cashboxResult.data && cashboxResult.data.length > 0) {
      const cashbox = cashboxResult.data[0];
      
      console.log('💰 بيانات الخزينة:');
      console.log(`   المبيعات: ${cashbox.sales_total || 0} د.ل`);
      console.log(`   المشتريات: ${cashbox.purchases_total || 0} د.ل`);
      console.log(`   مصاريف النقل: ${cashbox.transport_total || 0} د.ل`);
      console.log(`   الأرباح المحفوظة: ${cashbox.profit_total || 0} د.ل`);
      
      const calculatedProfit = (cashbox.sales_total || 0) - (cashbox.purchases_total || 0) - (cashbox.transport_total || 0);
      console.log(`   الأرباح المحسوبة: ${calculatedProfit} د.ل`);
      
      const difference = Math.abs((cashbox.profit_total || 0) - calculatedProfit);
      console.log(`   الفرق: ${difference} د.ل`);
      
      if (difference > 0.01) {
        console.log('⚠️ هناك فرق في حساب الأرباح!');
      } else {
        console.log('✅ حساب الأرباح صحيح');
      }
    }
  } catch (error) {
    console.error('❌ خطأ في عرض تفاصيل الأرباح:', error);
  }
}

// تصدير الدوال للاستخدام الخارجي
window.profitDiagnosis = {
  diagnoseProfitCalculation,
  fixProfitInDatabase,
  runProfitDiagnosisAndFix,
  showCurrentProfitDetails
};

console.log('✅ تم تحميل أداة تشخيص الأرباح');
console.log('📝 للاستخدام:');
console.log('   - profitDiagnosis.showCurrentProfitDetails() - عرض التفاصيل');
console.log('   - profitDiagnosis.runProfitDiagnosisAndFix() - تشخيص وإصلاح تلقائي');
