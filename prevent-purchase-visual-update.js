/**
 * منع التحديث البصري للأرباح أثناء عمليات الشراء
 * 
 * هذا الحل يمنع تحديث عرض الأرباح لفترة قصيرة بعد عمليات الشراء
 */

// متغير لتتبع آخر عملية شراء
let lastPurchaseTime = null;
let purchaseBlockDuration = 30000; // 30 ثانية

/**
 * تسجيل عملية شراء جديدة
 */
function registerPurchaseTransaction() {
  lastPurchaseTime = Date.now();
  console.log('[PURCHASE-BLOCK] تم تسجيل عملية شراء، منع تحديث الأرباح لمدة', purchaseBlockDuration / 1000, 'ثانية');
}

/**
 * فحص ما إذا كان يجب منع تحديث الأرباح
 */
function shouldBlockProfitUpdate() {
  if (!lastPurchaseTime) return false;
  
  const timeSincePurchase = Date.now() - lastPurchaseTime;
  const shouldBlock = timeSincePurchase < purchaseBlockDuration;
  
  if (shouldBlock) {
    const remainingTime = Math.ceil((purchaseBlockDuration - timeSincePurchase) / 1000);
    console.log('[PURCHASE-BLOCK] منع تحديث الأرباح، الوقت المتبقي:', remainingTime, 'ثانية');
  }
  
  return shouldBlock;
}

/**
 * تطبيق الحماية على دالة updateCashboxDirectly
 */
function applyPurchaseProtection() {
  console.log('🛡️ تطبيق الحماية من التحديث البصري للأرباح أثناء الشراء...');
  
  // حماية window.loadCashboxInfo
  if (window.loadCashboxInfo) {
    const originalLoadCashboxInfo = window.loadCashboxInfo;
    window.loadCashboxInfo = function(transactionType) {
      // إذا كان نوع المعاملة شراء، سجل العملية
      if (transactionType === 'purchase') {
        registerPurchaseTransaction();
        console.log('[PURCHASE-BLOCK] تجاهل تحديث الخزينة لعملية شراء');
        return;
      }
      
      // إذا كان هناك عملية شراء حديثة، امنع التحديث
      if (shouldBlockProfitUpdate()) {
        console.log('[PURCHASE-BLOCK] منع تحديث الخزينة بسبب عملية شراء حديثة');
        return;
      }
      
      // استدعاء الدالة الأصلية
      return originalLoadCashboxInfo.call(this, transactionType);
    };
  }
  
  // مراقبة أحداث الشراء
  const purchaseEvents = [
    'purchase-completed',
    'transaction-added'
  ];
  
  purchaseEvents.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      if (event.detail && event.detail.transaction_type === 'purchase') {
        registerPurchaseTransaction();
      }
    });
  });
  
  // مراقبة أحداث API للشراء
  if (window.api && window.api.on) {
    window.api.on('transaction-added', (data) => {
      if (data && data.transaction_type === 'purchase') {
        registerPurchaseTransaction();
      }
    });
    
    window.api.on('cashbox-updated', (data) => {
      if (data && data.transaction_type === 'purchase') {
        registerPurchaseTransaction();
      }
    });
  }
  
  console.log('✅ تم تطبيق الحماية من التحديث البصري للأرباح');
}

/**
 * إزالة الحماية
 */
function removePurchaseProtection() {
  console.log('🔓 إزالة الحماية من التحديث البصري للأرباح...');
  lastPurchaseTime = null;
  
  // يمكن إضافة كود لاستعادة الدوال الأصلية هنا إذا لزم الأمر
  
  console.log('✅ تم إزالة الحماية');
}

/**
 * تعيين مدة المنع
 */
function setPurchaseBlockDuration(seconds) {
  purchaseBlockDuration = seconds * 1000;
  console.log('[PURCHASE-BLOCK] تم تعيين مدة المنع إلى', seconds, 'ثانية');
}

/**
 * فرض تحديث الأرباح (تجاهل المنع)
 */
function forceUpdateProfits() {
  console.log('[PURCHASE-BLOCK] فرض تحديث الأرباح...');
  lastPurchaseTime = null;
  
  if (window.loadCashboxInfo) {
    window.loadCashboxInfo();
  }
  
  console.log('✅ تم فرض تحديث الأرباح');
}

/**
 * عرض حالة الحماية
 */
function showProtectionStatus() {
  console.log('📊 حالة الحماية من التحديث البصري:');
  console.log(`   - آخر عملية شراء: ${lastPurchaseTime ? new Date(lastPurchaseTime).toLocaleString() : 'لا توجد'}`);
  console.log(`   - مدة المنع: ${purchaseBlockDuration / 1000} ثانية`);
  
  if (lastPurchaseTime) {
    const timeSincePurchase = Date.now() - lastPurchaseTime;
    const isBlocked = timeSincePurchase < purchaseBlockDuration;
    const remainingTime = isBlocked ? Math.ceil((purchaseBlockDuration - timeSincePurchase) / 1000) : 0;
    
    console.log(`   - الحالة: ${isBlocked ? '🛡️ محمي' : '✅ غير محمي'}`);
    if (isBlocked) {
      console.log(`   - الوقت المتبقي: ${remainingTime} ثانية`);
    }
  } else {
    console.log('   - الحالة: ✅ غير محمي');
  }
}

/**
 * اختبار الحماية
 */
async function testPurchaseProtection() {
  console.log('🧪 اختبار الحماية من التحديث البصري...');
  
  // محاكاة عملية شراء
  registerPurchaseTransaction();
  
  // اختبار منع التحديث
  console.log('اختبار 1: محاولة تحديث بعد عملية شراء مباشرة');
  if (window.loadCashboxInfo) {
    window.loadCashboxInfo(); // يجب أن يتم منعه
  }
  
  // اختبار بعد فترة قصيرة
  setTimeout(() => {
    console.log('اختبار 2: محاولة تحديث بعد 5 ثواني');
    if (window.loadCashboxInfo) {
      window.loadCashboxInfo(); // يجب أن يتم منعه أيضاً
    }
  }, 5000);
  
  // اختبار بعد انتهاء فترة المنع
  setTimeout(() => {
    console.log('اختبار 3: محاولة تحديث بعد انتهاء فترة المنع');
    if (window.loadCashboxInfo) {
      window.loadCashboxInfo(); // يجب أن يعمل
    }
  }, purchaseBlockDuration + 1000);
}

// تطبيق الحماية تلقائياً عند تحميل الملف
applyPurchaseProtection();

// تصدير الدوال للاستخدام الخارجي
window.purchaseProtection = {
  registerPurchaseTransaction,
  shouldBlockProfitUpdate,
  applyPurchaseProtection,
  removePurchaseProtection,
  setPurchaseBlockDuration,
  forceUpdateProfits,
  showProtectionStatus,
  testPurchaseProtection
};

console.log('✅ تم تحميل حماية التحديث البصري للأرباح أثناء الشراء');
console.log('📝 للاستخدام:');
console.log('   - purchaseProtection.showProtectionStatus() - عرض الحالة');
console.log('   - purchaseProtection.setPurchaseBlockDuration(30) - تعيين مدة المنع');
console.log('   - purchaseProtection.forceUpdateProfits() - فرض التحديث');
console.log('   - purchaseProtection.testPurchaseProtection() - اختبار الحماية');
