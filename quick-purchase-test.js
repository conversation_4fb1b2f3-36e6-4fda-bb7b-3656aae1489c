/**
 * اختبار سريع للتحقق من إصلاح مشكلة تأثير عمليات الشراء على عرض الأرباح
 * 
 * هذا الملف يحتوي على اختبار سريع للتحقق من أن الإصلاح يعمل بشكل صحيح
 */

// متغيرات لتتبع الأحداث
let profitUpdateCount = 0;
let purchaseEventCount = 0;
let saleEventCount = 0;

/**
 * إعداد مراقبة الأحداث
 */
function setupQuickMonitoring() {
  console.log('🔍 إعداد مراقبة سريعة للأحداث...');
  
  // إعادة تعيين العدادات
  profitUpdateCount = 0;
  purchaseEventCount = 0;
  saleEventCount = 0;
  
  // مراقبة أحداث تحديث الأرباح
  const profitEvents = [
    'profits-updated',
    'auto-profits-updated',
    'dashboard-profits-updated',
    'financial-profits-updated'
  ];
  
  profitEvents.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      if (event.detail && event.detail.test === true) {
        profitUpdateCount++;
        console.log(`📊 حدث ربح مستلم: ${eventName}`, {
          transaction_type: event.detail.transaction_type,
          count: profitUpdateCount
        });
        
        if (event.detail.transaction_type === 'purchase') {
          purchaseEventCount++;
        } else if (event.detail.transaction_type === 'sale') {
          saleEventCount++;
        }
      }
    });
  });
  
  console.log('✅ تم إعداد مراقبة الأحداث');
}

/**
 * اختبار سريع لعملية الشراء
 */
async function quickPurchaseTest() {
  console.log('\n🛒 اختبار سريع لعملية الشراء...');
  
  // إعادة تعيين العدادات
  const initialProfitCount = profitUpdateCount;
  const initialPurchaseCount = purchaseEventCount;
  
  // محاكاة حدث شراء
  const purchaseEvent = new CustomEvent('profits-updated', {
    detail: {
      transaction_type: 'purchase',
      amount: 1000,
      total_profit: 5000,
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  
  window.dispatchEvent(purchaseEvent);
  
  // محاكاة حدث cashbox-updated-ui للشراء
  const cashboxEvent = new CustomEvent('cashbox-updated-ui', {
    detail: {
      transaction_type: 'purchase',
      amount: 1000,
      current_balance: 9000,
      profit_total: 5000,
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  
  window.dispatchEvent(cashboxEvent);
  
  // انتظار قصير
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // فحص النتائج
  const profitEventsTriggered = profitUpdateCount - initialProfitCount;
  const purchaseEventsTriggered = purchaseEventCount - initialPurchaseCount;
  
  console.log(`📊 نتائج اختبار الشراء:`);
  console.log(`   - أحداث الأرباح المستلمة: ${profitEventsTriggered}`);
  console.log(`   - أحداث الشراء المعالجة: ${purchaseEventsTriggered}`);
  
  const success = profitEventsTriggered === 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  return { success, profitEventsTriggered, purchaseEventsTriggered };
}

/**
 * اختبار سريع لعملية البيع
 */
async function quickSaleTest() {
  console.log('\n💵 اختبار سريع لعملية البيع...');
  
  // إعادة تعيين العدادات
  const initialProfitCount = profitUpdateCount;
  const initialSaleCount = saleEventCount;
  
  // محاكاة حدث بيع
  const saleEvent = new CustomEvent('profits-updated', {
    detail: {
      transaction_type: 'sale',
      amount: 1500,
      profit: 300,
      total_profit: 5300,
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  
  window.dispatchEvent(saleEvent);
  
  // انتظار قصير
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // فحص النتائج
  const profitEventsTriggered = profitUpdateCount - initialProfitCount;
  const saleEventsTriggered = saleEventCount - initialSaleCount;
  
  console.log(`📊 نتائج اختبار البيع:`);
  console.log(`   - أحداث الأرباح المستلمة: ${profitEventsTriggered}`);
  console.log(`   - أحداث البيع المعالجة: ${saleEventsTriggered}`);
  
  const success = profitEventsTriggered > 0 && saleEventsTriggered > 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  return { success, profitEventsTriggered, saleEventsTriggered };
}

/**
 * اختبار أحداث event-listeners.js
 */
async function quickEventListenersTest() {
  console.log('\n🔧 اختبار أحداث event-listeners.js...');

  // إعادة تعيين العدادات
  const initialProfitCount = profitUpdateCount;

  // محاكاة حدث CASHBOX_UPDATED للشراء
  if (window.api && window.api.emit) {
    try {
      window.api.emit('cashbox-updated', {
        transaction_type: 'purchase',
        amount: 1000,
        current_balance: 9000,
        profit_total: 5000,
        test: true,
        timestamp: new Date().toISOString()
      });

      console.log('تم إرسال حدث CASHBOX_UPDATED للشراء');
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }

  // محاكاة حدث direct-update للشراء
  const directUpdateEvent = new CustomEvent('direct-update', {
    detail: {
      transaction_type: 'purchase',
      amount: 1000,
      profit_total: 5000,
      test: true,
      timestamp: new Date().toISOString()
    }
  });

  window.dispatchEvent(directUpdateEvent);

  // انتظار قصير
  await new Promise(resolve => setTimeout(resolve, 300));

  // فحص النتائج
  const profitEventsTriggered = profitUpdateCount - initialProfitCount;

  console.log(`📊 نتائج اختبار event-listeners:`);
  console.log(`   - أحداث الأرباح المستلمة: ${profitEventsTriggered}`);

  const success = profitEventsTriggered === 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);

  return { success, profitEventsTriggered };
}

/**
 * تشغيل الاختبار السريع الكامل
 */
async function runQuickTest() {
  console.log('🚀 بدء الاختبار السريع للإصلاح...');
  console.log('=====================================');

  // إعداد المراقبة
  setupQuickMonitoring();

  // انتظار قصير للتأكد من الإعداد
  await new Promise(resolve => setTimeout(resolve, 500));

  // اختبار الشراء
  const purchaseResult = await quickPurchaseTest();

  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 500));

  // اختبار event-listeners
  const eventListenersResult = await quickEventListenersTest();

  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 500));

  // اختبار البيع
  const saleResult = await quickSaleTest();

  // النتائج النهائية
  console.log('\n🏁 النتائج النهائية:');
  console.log('=====================================');
  console.log(`${purchaseResult.success ? '✅' : '❌'} اختبار الشراء: ${purchaseResult.success ? 'نجح' : 'فشل'}`);
  console.log(`${eventListenersResult.success ? '✅' : '❌'} اختبار event-listeners: ${eventListenersResult.success ? 'نجح' : 'فشل'}`);
  console.log(`${saleResult.success ? '✅' : '❌'} اختبار البيع: ${saleResult.success ? 'نجح' : 'فشل'}`);

  const overallSuccess = purchaseResult.success && eventListenersResult.success && saleResult.success;
  console.log('=====================================');
  console.log(`🎯 النتيجة الإجمالية: ${overallSuccess ? '✅ الإصلاح يعمل بشكل صحيح' : '❌ يحتاج مراجعة'}`);

  if (overallSuccess) {
    console.log('🎉 تم إصلاح مشكلة تأثير عمليات الشراء على عرض الأرباح بنجاح!');
  } else {
    console.log('⚠️ هناك مشاكل في الإصلاح تحتاج إلى مراجعة.');

    if (!purchaseResult.success) {
      console.log('❌ مشكلة: عمليات الشراء لا تزال تؤثر على عرض الأرباح');
    }

    if (!eventListenersResult.success) {
      console.log('❌ مشكلة: مستمعي الأحداث لا يزالون يحدثون الأرباح لعمليات الشراء');
    }

    if (!saleResult.success) {
      console.log('❌ مشكلة: عمليات البيع لا تحدث الأرباح بشكل صحيح');
    }
  }

  return {
    overallSuccess,
    purchaseResult,
    eventListenersResult,
    saleResult,
    summary: {
      totalProfitEvents: profitUpdateCount,
      totalPurchaseEvents: purchaseEventCount,
      totalSaleEvents: saleEventCount
    }
  };
}

/**
 * إعادة تعيين الاختبار
 */
function resetQuickTest() {
  profitUpdateCount = 0;
  purchaseEventCount = 0;
  saleEventCount = 0;
  console.log('🔄 تم إعادة تعيين عدادات الاختبار');
}

// تصدير الدوال للاستخدام الخارجي
window.quickPurchaseTest = {
  runQuickTest,
  quickPurchaseTest,
  quickSaleTest,
  quickEventListenersTest,
  setupQuickMonitoring,
  resetQuickTest,
  getStats: () => ({ profitUpdateCount, purchaseEventCount, saleEventCount })
};

console.log('✅ تم تحميل اختبار الشراء السريع');
console.log('📝 للتشغيل: quickPurchaseTest.runQuickTest()');
