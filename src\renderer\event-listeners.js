/**
 * نظام مستمعي الأحداث في واجهة المستخدم
 * يقوم بإعداد مستمعي الأحداث للتحديثات التلقائية لجميع أجزاء التطبيق
 */

// تسجيل بدء تحميل الملف
console.log('جاري تحميل نظام مستمعي الأحداث في واجهة المستخدم...');

// قائمة بجميع أنواع الأحداث المدعومة
const EventTypes = {
  // أحداث المخزون
  INVENTORY_UPDATED: 'inventory-updated',
  INVENTORY_ITEM_ADDED: 'inventory-item-added',
  INVENTORY_ITEM_UPDATED: 'inventory-item-updated',
  INVENTORY_ITEM_DELETED: 'inventory-item-deleted',

  // أحداث الأصناف
  ITEM_ADDED: 'item-added',
  ITEM_UPDATED: 'item-updated',
  ITEM_DELETED: 'item-deleted',

  // أحداث المعاملات
  TRANSACTION_ADDED: 'transaction-added',
  TRANSACTION_UPDATED: 'transaction-updated',
  TRANSACTION_DELETED: 'transaction-deleted',

  // أحداث العملاء
  CUSTOMER_ADDED: 'customer-added',
  CUSTOMER_UPDATED: 'customer-updated',
  CUSTOMER_DELETED: 'customer-deleted',

  // أحداث الخزينة
  CASHBOX_UPDATED: 'cashbox-updated',
  CASHBOX_TRANSACTION_ADDED: 'cashbox-transaction-added',

  // أحداث عامة
  DATA_CHANGED: 'data-changed',
  REFRESH_NEEDED: 'refresh-needed'
};

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
  if (!window.api || !window.api.on) {
    console.error('واجهة API غير متوفرة لإعداد مستمعي الأحداث');
    return;
  }

  // مستمع لإضافة صنف جديد
  window.api.on(EventTypes.ITEM_ADDED, (data) => {
    console.log('تم استلام إشعار بإضافة صنف جديد:', data);

    // تحديث قائمة الأصناف بشكل فوري
    console.log('تحديث قائمة الأصناف بعد إضافة صنف جديد...');
    refreshItemsList();

    // تحديث قائمة المخزون بشكل فوري
    console.log('تحديث قائمة المخزون بعد إضافة صنف جديد...');
    refreshInventoryList();

    // تحديث جدول المشتريات أيضاً
    console.log('تحديث جدول المشتريات بعد إضافة صنف جديد...');
    refreshTransactionsList();

    // إعادة تحميل الصفحة إذا كانت صفحة الأصناف مفتوحة
    if (window.location.hash.includes('#/items')) {
      console.log('صفحة الأصناف مفتوحة، جاري إعادة تحميل البيانات...');
      setTimeout(() => {
        if (typeof window.loadItems === 'function') {
          console.log('استدعاء window.loadItems() لتحديث قائمة الأصناف في الواجهة');
          window.loadItems();
        } else {
          console.log('دالة window.loadItems غير متوفرة، محاولة تحديث الصفحة بطريقة أخرى');
          // محاولة استدعاء API مباشرة لتحديث الأصناف
          if (window.api && window.api.invoke) {
            window.api.invoke('refresh-needed', { target: 'items' })
              .then(() => {
                console.log('تم إرسال طلب تحديث الأصناف بنجاح');
              })
              .catch(error => {
                console.error('خطأ في إرسال طلب تحديث الأصناف:', error);
              });
          }
        }
      }, 100); // تقليل الفترة الزمنية للاستجابة بشكل أسرع
    }

    // إعادة تحميل الصفحة إذا كانت صفحة المشتريات مفتوحة - تم تعديله لتجنب التحديثات المتكررة
    if (window.location.hash.includes('#/purchases')) {
      console.log('صفحة المشتريات مفتوحة، جاري إعادة تحميل البيانات مرة واحدة...');
      if (typeof window.loadTransactions === 'function') {
        window.loadTransactions();
      }
    }

    // إعادة تحميل الصفحة إذا كانت صفحة المخزون مفتوحة
    if (window.location.hash.includes('#/inventory')) {
      console.log('صفحة المخزون مفتوحة، جاري إعادة تحميل البيانات...');
      setTimeout(() => {
        if (typeof window.loadInventory === 'function') {
          window.loadInventory(true); // تمرير true لتجاوز التخزين المؤقت
        }
      }, 500);
    }

    showNotification(`تم إضافة الصنف "${data.name}" بنجاح وتحديث المخزون`, 'success');
  });

  // مستمع لتحديث صنف
  window.api.on(EventTypes.ITEM_UPDATED, (data) => {
    console.log('[ITEM-UPDATE] تم استلام إشعار بتحديث صنف:', data);

    // التحقق من صحة البيانات
    if (!data || !data.id) {
      console.error('[ITEM-UPDATE] بيانات الصنف المستلمة غير صالحة:', data);
      return;
    }

    // تسجيل معلومات إضافية للتشخيص
    console.log('[ITEM-UPDATE] معرف الصنف المحدث:', data.id);
    console.log('[ITEM-UPDATE] اسم الصنف المحدث:', data.name);
    console.log('[ITEM-UPDATE] وحدة القياس المحدثة:', data.unit);
    console.log('[ITEM-UPDATE] الطابع الزمني:', data.timestamp);

    // محاولة تحديث الصنف في قائمة الأصناف الحالية
    try {
      // تحديث قائمة الأصناف بشكل إجباري
      if (typeof window.loadItems === 'function') {
        console.log('[ITEM-UPDATE] جاري تحديث قائمة الأصناف بشكل إجباري...');
        window.loadItems(true); // تمرير true لتجاوز التخزين المؤقت
      } else {
        console.log('[ITEM-UPDATE] وظيفة تحديث قائمة الأصناف غير متوفرة، استخدام الوظيفة البديلة...');
        refreshItemsList();
      }

      // محاولة تحديث الصنف في قائمة الأصناف الحالية مباشرة
      if (typeof window.updateItemInList === 'function') {
        console.log('[ITEM-UPDATE] محاولة تحديث الصنف في القائمة الحالية مباشرة...');
        window.updateItemInList(data);
      }
    } catch (updateError) {
      console.error('[ITEM-UPDATE] خطأ في تحديث قائمة الأصناف:', updateError);

      // محاولة استدعاء API مباشرة في حالة حدوث خطأ
      if (window.api && window.api.items && window.api.items.getAll) {
        console.log('[ITEM-UPDATE] محاولة استدعاء API مباشرة لتحديث الأصناف...');
        window.api.items.getAll(true)
          .then(items => {
            console.log(`[ITEM-UPDATE] تم استرجاع ${items.length} صنف من قاعدة البيانات`);
          })
          .catch(apiError => {
            console.error('[ITEM-UPDATE] خطأ في استدعاء API مباشرة:', apiError);
          });
      }
    }

    // تحديث قائمة المخزون بغض النظر عن نوع التحديث
    console.log('[ITEM-UPDATE] جاري تحديث قائمة المخزون...');
    refreshInventoryList();

    // تحديث الصنف في النافذة الحالية إذا كان موجودًا
    if (typeof window.updateItemInCurrentList === 'function') {
      try {
        console.log('[ITEM-UPDATE] جاري تحديث الصنف في القائمة الحالية...');
        const result = window.updateItemInCurrentList(data);
        console.log('[ITEM-UPDATE] نتيجة تحديث الصنف في القائمة الحالية:', result);
      } catch (updateError) {
        console.error('[ITEM-UPDATE] خطأ في تحديث الصنف في القائمة الحالية:', updateError);
      }
    } else {
      console.log('[ITEM-UPDATE] وظيفة تحديث الصنف في القائمة الحالية غير متوفرة');
    }

    // إعادة تحميل الصفحة إذا كانت صفحة الأصناف مفتوحة
    if (window.location.hash.includes('#/items')) {
      console.log('[ITEM-UPDATE] صفحة الأصناف مفتوحة، جاري إعادة تحميل البيانات بعد فترة قصيرة...');
      setTimeout(() => {
        if (typeof window.loadItems === 'function') {
          console.log('[ITEM-UPDATE] إعادة تحميل الأصناف بعد تأخير...');
          window.loadItems(true); // تمرير true لتجاوز التخزين المؤقت
        }
      }, 500);
    }

    // إذا كانت صفحة المشتريات مفتوحة، نقوم بتحديث قائمة الأصناف فيها
    if (window.location.hash.includes('#/purchases')) {
      console.log('[ITEM-UPDATE] صفحة المشتريات مفتوحة، جاري تحديث قائمة الأصناف...');
      if (typeof window.refreshItemsList === 'function') {
        window.refreshItemsList();
      }
    }

    showNotification(`تم تحديث الصنف "${data.name}" بنجاح`, 'success');
  });

  // مستمع لحذف صنف
  window.api.on(EventTypes.ITEM_DELETED, (data) => {
    console.log('تم استلام إشعار بحذف صنف:', data);
    refreshItemsList();
    refreshInventoryList();
    showNotification(`تم حذف الصنف "${data.name}" بنجاح`, 'success');
  });

  // مستمع لتحديث المخزون
  window.api.on(EventTypes.INVENTORY_UPDATED, (data) => {
    console.log('تم استلام إشعار بتحديث المخزون:', data);

    // تحديث قائمة المخزون بشكل فوري
    console.log('تحديث قائمة المخزون بعد استلام إشعار تحديث المخزون...');
    refreshInventoryList();

    // إذا كان التحديث بسبب إضافة صنف جديد، نقوم بتحديث قائمة المشتريات أيضاً
    if (data.operation === 'add-item') {
      console.log('تحديث قائمة المشتريات بعد إضافة صنف جديد...');
      refreshTransactionsList();
    }

    // إعادة تحميل الصفحة إذا كانت صفحة المخزون مفتوحة
    if (window.location.hash.includes('#/inventory')) {
      console.log('صفحة المخزون مفتوحة، جاري إعادة تحميل البيانات...');
      setTimeout(() => {
        if (typeof window.loadInventory === 'function') {
          window.loadInventory(true); // تمرير true لتجاوز التخزين المؤقت
        }
      }, 500);
    }

    showNotification(`تم تحديث المخزون للصنف "${data.name}" بنجاح`, 'success');
  });

  // مستمع لإضافة معاملة جديدة
  window.api.on(EventTypes.TRANSACTION_ADDED, (data) => {
    console.log('تم استلام إشعار بإضافة معاملة جديدة:', data);
    refreshTransactionsList();
    refreshInventoryList();
    refreshCashboxInfo();

    // تحديث الأرباح بعد عمليات البيع والإرجاع
    if (data.transaction_type === 'sale' || data.transaction_type === 'return') {
      console.log('تحديث الأرباح بعد عملية ' + getTransactionTypeName(data.transaction_type));
      refreshProfits();
    }

    showNotification(`تم إضافة معاملة جديدة من نوع "${getTransactionTypeName(data.transaction_type)}" بنجاح`, 'success');
  });

  // مستمع لتحديث الخزينة - مع فحص نوع المعاملة
  window.api.on(EventTypes.CASHBOX_UPDATED, (data) => {
    console.log('[CASHBOX-EVENT] تم استلام إشعار بتحديث الخزينة:', data);

    // تسجيل معلومات إضافية للتشخيص
    if (data) {
      console.log('[CASHBOX-EVENT] نوع المعاملة:', data.transaction_type || 'غير محدد');
      console.log('[CASHBOX-EVENT] المبلغ:', data.amount || 0);
      console.log('[CASHBOX-EVENT] الرصيد الحالي:', data.current_balance || 'غير محدد');
      console.log('[CASHBOX-EVENT] إجمالي المبيعات:', data.sales_total || 'غير محدد');
      console.log('[CASHBOX-EVENT] إجمالي المشتريات:', data.purchases_total || 'غير محدد');
      console.log('[CASHBOX-EVENT] إجمالي المرتجعات:', data.returns_total || 'غير محدد');
      console.log('[CASHBOX-EVENT] إجمالي الأرباح:', data.profit_total || 'غير محدد');
    }

    // تحديث معلومات الخزينة مع تمرير نوع المعاملة
    console.log('[CASHBOX-EVENT] جاري تحديث معلومات الخزينة...');
    refreshCashboxInfo(data?.transaction_type);

    // تحديث الأرباح فقط لعمليات البيع
    if (data && data.transaction_type) {
      if (data.transaction_type === 'sale') {
        console.log('[CASHBOX-EVENT] تحديث الأرباح بعد عملية بيع');
        refreshProfits();
      } else {
        console.log(`[CASHBOX-EVENT] تجاهل تحديث الأرباح لعملية ${data.transaction_type} (إصلاح الخطأ البصري)`);
      }
    } else {
      // للتوافق مع الأحداث القديمة
      console.log('[CASHBOX-EVENT] تحديث الأرباح (حدث بدون نوع معاملة محدد)');
      refreshProfits();
    }

    // تحديث المعاملات إذا كانت صفحة المعاملات مفتوحة
    if (window.location.hash.includes('#/transactions') ||
        window.location.hash.includes('#/sales') ||
        window.location.hash.includes('#/purchases')) {
      console.log('[CASHBOX-EVENT] جاري تحديث المعاملات بعد تحديث الخزينة...');
      refreshTransactionsList();
    }

    // عرض إشعار بنوع المعاملة
    let notificationMessage = `تم تحديث الخزينة بنجاح`;
    if (data && data.transaction_type) {
      const transactionTypeMap = {
        'sale': 'بيع',
        'purchase': 'شراء',
        'return': 'استرجاع'
      };
      const transactionTypeArabic = transactionTypeMap[data.transaction_type] || data.transaction_type;
      notificationMessage = `تم تحديث الخزينة بعد عملية ${transactionTypeArabic} بمبلغ ${data.amount || 0}`;
    }

    showNotification(notificationMessage, 'success');
  });

  // مستمع لإضافة عميل جديد
  window.api.on(EventTypes.CUSTOMER_ADDED, (data) => {
    console.log('تم استلام إشعار بإضافة عميل جديد:', data);
    refreshCustomersList();
    showNotification(`تم إضافة العميل "${data.name}" بنجاح`, 'success');
  });

  // مستمع لتحديث عميل
  window.api.on(EventTypes.CUSTOMER_UPDATED, (data) => {
    console.log('تم استلام إشعار بتحديث عميل:', data);
    refreshCustomersList();
    showNotification(`تم تحديث العميل "${data.name}" بنجاح`, 'success');
  });

  // مستمع لتحديث الأرباح التلقائي الفوري - مع فحص نوع المعاملة
  window.api.on(EventTypes.PROFITS_UPDATED, (data) => {
    console.log('[AUTO-PROFITS-EVENT] تم استلام إشعار بتحديث الأرباح التلقائي:', data);

    // تحديث الأرباح فقط لعمليات البيع
    if (data && data.transaction_type) {
      if (data.transaction_type === 'sale') {
        console.log('[AUTO-PROFITS-EVENT] تحديث الأرباح بعد عملية بيع');
        refreshProfits();
      } else {
        console.log(`[AUTO-PROFITS-EVENT] تجاهل تحديث الأرباح لعملية ${data.transaction_type} (إصلاح الخطأ البصري)`);
        return; // لا نرسل أي أحداث إضافية
      }
    } else {
      // للتوافق مع الأحداث القديمة
      console.log('[AUTO-PROFITS-EVENT] تحديث الأرباح (حدث بدون نوع معاملة محدد)');
      refreshProfits();
    }

    // إذا كان التحديث تلقائياً، لا نعرض إشعار للمستخدم
    if (!data.auto_update) {
      showNotification(`تم تحديث قيم الربح بنجاح (${data.updatedCount || 0} معاملة)`, 'success');
    } else {
      console.log('[AUTO-PROFITS-EVENT] تم تحديث الأرباح تلقائياً بدون إشعار للمستخدم');
    }

    // إرسال أحداث متعددة فقط لعمليات البيع
    if (!data.transaction_type || data.transaction_type === 'sale') {
      const profitsEvent = new CustomEvent('profits-updated', { detail: data });
      window.dispatchEvent(profitsEvent);

      const autoProfitsEvent = new CustomEvent('auto-profits-updated', { detail: data });
      window.dispatchEvent(autoProfitsEvent);

      // إرسال حدث تحديث مباشر إضافي
      const directEvent = new CustomEvent('direct-update', { detail: { ...data, source: 'profits' } });
      window.dispatchEvent(directEvent);
    }
  });

  // مستمع عام للتحديثات
  window.api.on(EventTypes.REFRESH_NEEDED, (data) => {
    console.log('[REFRESH-EVENT] تم استلام إشعار بالحاجة للتحديث:', data);

    // تسجيل معلومات إضافية للتشخيص
    if (data) {
      console.log('[REFRESH-EVENT] الهدف:', data.target || 'غير محدد');
      console.log('[REFRESH-EVENT] العملية:', data.operation || 'غير محدد');
      console.log('[REFRESH-EVENT] الطابع الزمني:', data.timestamp || 'غير محدد');
    }

    // تحديث الجزء المطلوب
    switch (data.target) {
      case 'items':
        console.log('[REFRESH-EVENT] جاري تحديث قائمة الأصناف...');
        refreshItemsList();
        break;
      case 'inventory':
        console.log('[REFRESH-EVENT] جاري تحديث قائمة المخزون...');
        refreshInventoryList();
        break;
      case 'transactions':
        console.log('[REFRESH-EVENT] جاري تحديث قائمة المعاملات...');
        refreshTransactionsList();
        break;
      case 'customers':
        console.log('[REFRESH-EVENT] جاري تحديث قائمة العملاء...');
        refreshCustomersList();
        break;
      case 'cashbox':
        console.log('[REFRESH-EVENT] جاري تحديث معلومات الخزينة...');
        refreshCashboxInfo();

        // إذا كانت صفحة الخزينة مفتوحة، نقوم بإعادة تحميل البيانات بعد فترة قصيرة
        if (window.location.hash.includes('#/cashbox')) {
          console.log('[REFRESH-EVENT] صفحة الخزينة مفتوحة، جاري إعادة تحميل البيانات بعد فترة قصيرة...');
          setTimeout(() => {
            if (typeof window.loadCashbox === 'function') {
              console.log('[REFRESH-EVENT] استدعاء window.loadCashbox()...');
              window.loadCashbox();
            }
          }, 500);
        }
        break;
      case 'reports':
        console.log('[REFRESH-EVENT] جاري تحديث التقارير...');
        refreshProfits();

        // إذا كانت صفحة التقارير مفتوحة، نقوم بإرسال حدث خاص لتحديث واجهة المستخدم
        if (window.location.hash.includes('#/reports')) {
          console.log('[REFRESH-EVENT] صفحة التقارير مفتوحة، جاري إرسال حدث تحديث خاص...');
          // إرسال حدث مخصص لتحديث التقارير
          const event = new CustomEvent('profits-updated', {
            detail: {
              operation: data.operation,
              timestamp: data.timestamp,
              force_refresh: true
            }
          });
          window.dispatchEvent(event);
        }
        break;
      case 'all':
        console.log('[REFRESH-EVENT] جاري تحديث كل شيء...');
        refreshAll();
        break;
      default:
        console.log('[REFRESH-EVENT] هدف غير معروف، جاري تحديث كل شيء...');
        refreshAll();
    }
  });

  // مستمع للإشعار المباشر بتحديث الأرباح - مع فحص نوع المعاملة
  window.api.on('direct-profits-update', (data) => {
    console.log('[DIRECT-PROFITS-UPDATE] تم استلام إشعار مباشر بتحديث الأرباح:', data);

    // تحديث الأرباح فقط لعمليات البيع
    if (data && data.transaction_type) {
      if (data.transaction_type === 'sale') {
        console.log('[DIRECT-PROFITS-UPDATE] تحديث الأرباح بعد عملية بيع');
        refreshProfits();

        // إرسال حدث مخصص لتحديث التقارير
        const event = new CustomEvent('profits-updated', { detail: data });
        window.dispatchEvent(event);
      } else {
        console.log(`[DIRECT-PROFITS-UPDATE] تجاهل تحديث الأرباح لعملية ${data.transaction_type} (إصلاح الخطأ البصري)`);
      }
    } else {
      // للتوافق مع الأحداث القديمة
      console.log('[DIRECT-PROFITS-UPDATE] تحديث الأرباح (حدث بدون نوع معاملة محدد)');
      refreshProfits();

      // إرسال حدث مخصص لتحديث التقارير
      const event = new CustomEvent('profits-updated', { detail: data });
      window.dispatchEvent(event);
    }
  });

  // مستمع للتحديث المباشر الجديد - مع فحص نوع المعاملة
  window.api.on('direct-update', (data) => {
    console.log('[DIRECT-UPDATE] تم استلام إشعار تحديث مباشر:', data);

    // تحديث فوري للمعاملات والخزينة مع تمرير نوع المعاملة
    refreshTransactionsList();
    refreshCashboxInfo(data?.transaction_type);

    // تحديث الأرباح فقط لعمليات البيع
    if (data && data.transaction_type) {
      if (data.transaction_type === 'sale') {
        console.log('[DIRECT-UPDATE] تحديث الأرباح بعد عملية بيع');
        refreshProfits();
      } else {
        console.log(`[DIRECT-UPDATE] تجاهل تحديث الأرباح لعملية ${data.transaction_type} (إصلاح الخطأ البصري)`);
      }
    } else {
      // للتوافق مع الأحداث القديمة
      console.log('[DIRECT-UPDATE] تحديث الأرباح (حدث بدون نوع معاملة محدد)');
      refreshProfits();
    }

    // إرسال حدث مخصص للتحديث المباشر
    const directEvent = new CustomEvent('direct-update', { detail: data });
    window.dispatchEvent(directEvent);

    // إرسال أحداث إضافية مع فحص نوع المعاملة
    if (data && data.transaction_type === 'sale') {
      const profitsEvent = new CustomEvent('profits-updated', { detail: data });
      window.dispatchEvent(profitsEvent);
    }

    const transactionsEvent = new CustomEvent('transactions-refreshed', { detail: data });
    window.dispatchEvent(transactionsEvent);
  });

  console.log('تم إعداد مستمعي الأحداث بنجاح');
}

/**
 * تحديث كل شيء
 */
function refreshAll() {
  console.log('[REFRESH-ALL] بدء تحديث كل شيء...');

  // تحديث قائمة الأصناف
  refreshItemsList();

  // تحديث قائمة المخزون
  refreshInventoryList();

  // تحديث قائمة المعاملات
  refreshTransactionsList();

  // تحديث قائمة العملاء
  refreshCustomersList();

  // تحديث معلومات الخزينة
  refreshCashboxInfo();

  // تحديث الأرباح
  refreshProfits();

  console.log('[REFRESH-ALL] تم تحديث كل شيء بنجاح');
}

/**
 * تحديث قائمة الأصناف
 */
function refreshItemsList() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadItems === 'function') {
    console.log('جاري تحديث قائمة الأصناف...');

    try {
      // استدعاء دالة تحديث قائمة الأصناف
      window.loadItems();
      console.log('تم استدعاء دالة تحديث قائمة الأصناف بنجاح');

      // تم تعديل هذا الجزء لتجنب التحديثات المتكررة التي تسبب تجميد الواجهة
      // لا نقوم بإعادة تحميل البيانات مرة أخرى بعد فترة قصيرة
    } catch (error) {
      console.error('خطأ في تحديث قائمة الأصناف:', error);

      // محاولة استدعاء API مباشرة في حالة حدوث خطأ
      tryDirectApiCall();
    }
  } else {
    console.log('دالة تحديث قائمة الأصناف غير متوفرة في هذه الصفحة');

    // محاولة استدعاء API مباشرة لتحديث الأصناف
    tryDirectApiCall();
  }

  // دالة مساعدة لاستدعاء API مباشرة
  function tryDirectApiCall() {
    if (window.api && window.api.invoke) {
      console.log('محاولة استدعاء API مباشرة لتحديث الأصناف...');

      // محاولة استدعاء get-all-items مباشرة
      window.api.invoke('get-all-items')
        .then(items => {
          console.log(`تم استرجاع ${items.length} صنف من قاعدة البيانات`);

          // إرسال إشعار بالحاجة للتحديث
          return window.api.invoke('refresh-needed', { target: 'items' });
        })
        .then(() => {
          console.log('تم إرسال طلب تحديث الأصناف بنجاح');
        })
        .catch(error => {
          console.error('خطأ في إرسال طلب تحديث الأصناف:', error);
        });
    }
  }
}

/**
 * تحديث قائمة المخزون
 */
function refreshInventoryList() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadInventory === 'function') {
    console.log('جاري تحديث قائمة المخزون...');

    try {
      // تمرير true لتجاوز التخزين المؤقت
      window.loadInventory(true);
      console.log('تم استدعاء دالة تحديث قائمة المخزون بنجاح');

      // تم تعديل هذا الجزء لتجنب التحديثات المتكررة التي تسبب تجميد الواجهة
      // لا نقوم بإعادة تحميل البيانات مرة أخرى بعد فترة قصيرة
    } catch (error) {
      console.error('خطأ في تحديث قائمة المخزون:', error);
    }
  } else {
    console.log('دالة تحديث قائمة المخزون غير متوفرة في هذه الصفحة');

    // محاولة استدعاء API مباشرة لتحديث المخزون
    if (window.api && window.api.invoke) {
      console.log('محاولة استدعاء API مباشرة لتحديث المخزون...');
      window.api.invoke('refresh-needed', { target: 'inventory' })
        .then(() => {
          console.log('تم إرسال طلب تحديث المخزون بنجاح');
        })
        .catch(error => {
          console.error('خطأ في إرسال طلب تحديث المخزون:', error);
        });
    }
  }
}

/**
 * تحديث قائمة المعاملات
 */
function refreshTransactionsList() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadTransactions === 'function') {
    console.log('جاري تحديث قائمة المعاملات...');

    try {
      window.loadTransactions();
      console.log('تم استدعاء دالة تحديث قائمة المعاملات بنجاح');

      // تم تعديل هذا الجزء لتجنب التحديثات المتكررة التي تسبب تجميد الواجهة
      // لا نقوم بإعادة تحميل البيانات مرة أخرى بعد فترة قصيرة
    } catch (error) {
      console.error('خطأ في تحديث قائمة المعاملات:', error);
    }
  } else {
    console.log('دالة تحديث قائمة المعاملات غير متوفرة في هذه الصفحة');

    // محاولة استدعاء API مباشرة لتحديث المعاملات
    if (window.api && window.api.invoke) {
      console.log('محاولة استدعاء API مباشرة لتحديث المعاملات...');
      window.api.invoke('refresh-needed', { target: 'transactions' })
        .then(() => {
          console.log('تم إرسال طلب تحديث المعاملات بنجاح');
        })
        .catch(error => {
          console.error('خطأ في إرسال طلب تحديث المعاملات:', error);
        });
    }
  }
}

/**
 * تحديث قائمة العملاء
 */
function refreshCustomersList() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadCustomers === 'function') {
    console.log('جاري تحديث قائمة العملاء...');
    window.loadCustomers();
  } else {
    console.log('دالة تحديث قائمة العملاء غير متوفرة في هذه الصفحة');
  }
}

/**
 * تحديث معلومات الخزينة فورياً
 */
function refreshCashboxInfo(transactionType = null) {
  console.log('[INSTANT-CASHBOX] بدء التحديث الفوري لمعلومات الخزينة...', 'نوع المعاملة:', transactionType);

  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.loadCashboxInfo === 'function') {
    console.log('[INSTANT-CASHBOX] استدعاء window.loadCashboxInfo()...');
    try {
      window.loadCashboxInfo(transactionType);
      console.log('[INSTANT-CASHBOX] تم التحديث الفوري لمعلومات الخزينة');
    } catch (error) {
      console.error('[INSTANT-CASHBOX] خطأ في التحديث الفوري:', error);
    }
  } else {
    console.log('[INSTANT-CASHBOX] دالة التحديث غير متوفرة في هذه الصفحة');

    // محاولة استدعاء API مباشرة لتحديث الخزينة (فقط لعمليات البيع)
    if (window.api && window.api.invoke && (!transactionType || transactionType === 'sale')) {
      console.log('[INSTANT-CASHBOX] استدعاء API مباشرة للتحديث الفوري...');
      window.api.invoke('refresh-needed', { target: 'cashbox', instant: true, transaction_type: transactionType })
        .then(() => {
          console.log('[INSTANT-CASHBOX] تم إرسال طلب التحديث الفوري بنجاح');
        })
        .catch(error => {
          console.error('[INSTANT-CASHBOX] خطأ في إرسال طلب التحديث الفوري:', error);
        });
    } else if (transactionType === 'purchase') {
      console.log('[INSTANT-CASHBOX] تجاهل تحديث الخزينة لعملية شراء (إصلاح الخطأ البصري)');
    }
  }
}

/**
 * تحديث الأرباح
 */
function refreshProfits() {
  // التحقق من وجود الدالة في النافذة الحالية
  if (typeof window.updateProfitValues === 'function') {
    console.log('جاري تحديث الأرباح باستخدام updateProfitValues...');
    window.updateProfitValues();
  } else if (typeof window.fetchProfitsReport === 'function') {
    console.log('جاري تحديث تقرير الأرباح باستخدام fetchProfitsReport...');
    window.fetchProfitsReport();
  } else {
    console.log('دالة تحديث الأرباح غير متوفرة في هذه الصفحة، محاولة تحديث عام...');

    // استدعاء API لتحديث قيم الربح بغض النظر عن الصفحة الحالية
    if (window.api && window.api.invoke) {
      console.log('استدعاء API لتحديث قيم الربح...');
      window.api.invoke('update-profit-values')
        .then(result => {
          console.log('تم تحديث قيم الربح بنجاح:', result);

          // إذا كنا في صفحة التقارير، نقوم بإرسال حدث خاص لتحديث واجهة المستخدم
          if (window.location.hash.includes('#/reports')) {
            console.log('إرسال حدث تحديث خاص لصفحة التقارير...');
            // إرسال حدث مخصص لتحديث التقارير
            const event = new CustomEvent('profits-updated', { detail: result });
            window.dispatchEvent(event);
          }
        })
        .catch(error => {
          console.error('خطأ في تحديث قيم الربح:', error);
        });
    }
  }
}

/**
 * تحديث كل شيء
 */
function refreshAll() {
  refreshItemsList();
  refreshInventoryList();
  refreshTransactionsList();
  refreshCustomersList();
  refreshCashboxInfo();
  refreshProfits();
}

/**
 * عرض إشعار للمستخدم - تم تعطيله
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
  // تسجيل الإشعار في وحدة التحكم فقط دون عرضه للمستخدم
  console.log(`تم تجاهل الإشعار (${type}): ${message}`);

  // لا نقوم بعرض أي إشعارات للمستخدم
  // تم تعطيل جميع طرق عرض الإشعارات
}

/**
 * الحصول على اسم نوع المعاملة
 * @param {string} type - نوع المعاملة
 * @returns {string} - اسم نوع المعاملة
 */
function getTransactionTypeName(type) {
  switch (type) {
    case 'purchase': return 'شراء';
    case 'sale': return 'بيع';
    case 'receiving': return 'استلام';
    case 'withdrawal': return 'صرف';
    default: return type;
  }
}

// إعداد المستمعين عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  console.log('جاري إعداد مستمعي الأحداث بعد تحميل الصفحة...');
  setupEventListeners();
});

// تصدير الدوال للاستخدام الخارجي
window.eventListeners = {
  setupEventListeners,
  refreshItemsList,
  refreshInventoryList,
  refreshTransactionsList,
  refreshCustomersList,
  refreshCashboxInfo,
  refreshProfits,
  refreshAll
};

console.log('تم تحميل نظام مستمعي الأحداث بنجاح');
