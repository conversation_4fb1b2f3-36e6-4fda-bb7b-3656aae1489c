/**
 * اختبار إصلاح الخطأ البصري في عرض الخزينة
 * 
 * هذا الملف يختبر تحديداً مشكلة تأثير عمليات الشراء على عرض الأرباح في صفحة الخزينة
 */

// متغيرات لتتبع تحديثات الخزينة
let cashboxUpdateEvents = [];
let profitDisplayUpdates = [];

/**
 * إعداد مراقبة تحديثات الخزينة
 */
function setupCashboxMonitoring() {
  console.log('🔍 إعداد مراقبة تحديثات الخزينة...');
  
  // إعادة تعيين المصفوفات
  cashboxUpdateEvents = [];
  profitDisplayUpdates = [];
  
  // مراقبة أحداث تحديث الخزينة
  const cashboxEvents = [
    'cashbox-updated-ui',
    'direct-cashbox-update',
    'refresh-needed'
  ];
  
  cashboxEvents.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      const eventData = {
        type: eventName,
        timestamp: new Date().toISOString(),
        detail: event.detail,
        transaction_type: event.detail?.transaction_type,
        test: event.detail?.test
      };
      
      // تسجيل الحدث فقط إذا كان اختبار
      if (event.detail?.test === true) {
        cashboxUpdateEvents.push(eventData);
        console.log(`💰 حدث تحديث خزينة مستلم: ${eventName}`, {
          transaction_type: eventData.transaction_type,
          count: cashboxUpdateEvents.length
        });
        
        // إذا كان حدث تحديث أرباح
        if (eventName.includes('cashbox') || eventName.includes('refresh')) {
          profitDisplayUpdates.push(eventData);
        }
      }
    });
  });
  
  // مراقبة استدعاءات window.loadCashboxInfo
  if (window.loadCashboxInfo) {
    const originalLoadCashboxInfo = window.loadCashboxInfo;
    window.loadCashboxInfo = function(transactionType) {
      console.log('📊 تم استدعاء window.loadCashboxInfo مع نوع المعاملة:', transactionType);
      
      // تسجيل الاستدعاء
      profitDisplayUpdates.push({
        type: 'loadCashboxInfo',
        timestamp: new Date().toISOString(),
        transaction_type: transactionType,
        test: true
      });
      
      // استدعاء الدالة الأصلية
      return originalLoadCashboxInfo.call(this, transactionType);
    };
  }
  
  console.log('✅ تم إعداد مراقبة تحديثات الخزينة');
}

/**
 * اختبار عملية شراء وتأثيرها على عرض الخزينة
 */
async function testPurchaseImpactOnCashboxDisplay() {
  console.log('\n🛒 اختبار تأثير عملية الشراء على عرض الخزينة...');
  
  // إعادة تعيين العدادات
  const initialCounts = {
    cashboxEvents: cashboxUpdateEvents.length,
    profitUpdates: profitDisplayUpdates.length
  };
  
  // 1. محاكاة حدث CASHBOX_UPDATED للشراء
  if (window.api && window.api.emit) {
    try {
      console.log('إرسال حدث CASHBOX_UPDATED للشراء...');
      window.api.emit('cashbox-updated', {
        transaction_type: 'purchase',
        amount: 1000,
        current_balance: 9000,
        profit_total: 5000,
        test: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }
  
  // 2. محاكاة حدث direct-cashbox-update للشراء
  const directCashboxEvent = new CustomEvent('direct-cashbox-update', {
    detail: {
      transaction_type: 'purchase',
      amount: 1000,
      current_balance: 9000,
      profit_total: 5000,
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  window.dispatchEvent(directCashboxEvent);
  
  // 3. محاكاة حدث refresh-needed للشراء
  const refreshNeededEvent = new CustomEvent('refresh-needed', {
    detail: {
      target: 'cashbox',
      transaction_type: 'purchase',
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  window.dispatchEvent(refreshNeededEvent);
  
  // 4. اختبار استدعاء window.loadCashboxInfo مباشرة
  if (window.loadCashboxInfo) {
    console.log('اختبار استدعاء window.loadCashboxInfo مباشرة للشراء...');
    window.loadCashboxInfo('purchase');
  }
  
  // انتظار قصير للسماح بمعالجة الأحداث
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // فحص النتائج
  const finalCounts = {
    cashboxEvents: cashboxUpdateEvents.length - initialCounts.cashboxEvents,
    profitUpdates: profitDisplayUpdates.length - initialCounts.profitUpdates
  };
  
  console.log(`📊 نتائج اختبار تأثير الشراء على عرض الخزينة:`);
  console.log(`   - أحداث تحديث الخزينة: ${finalCounts.cashboxEvents}`);
  console.log(`   - تحديثات عرض الأرباح: ${finalCounts.profitUpdates}`);
  
  // التحقق من أن عمليات الشراء لا تحدث عرض الأرباح
  const purchaseRelatedUpdates = profitDisplayUpdates.slice(-finalCounts.profitUpdates)
    .filter(update => update.transaction_type === 'purchase');
  
  console.log(`   - تحديثات مرتبطة بالشراء: ${purchaseRelatedUpdates.length}`);
  
  const success = purchaseRelatedUpdates.length === 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  if (!success) {
    console.log('❌ تفاصيل التحديثات المرتبطة بالشراء:');
    purchaseRelatedUpdates.forEach((update, index) => {
      console.log(`   ${index + 1}. ${update.type} - ${update.transaction_type}`);
    });
  }
  
  return { success, ...finalCounts, purchaseRelatedUpdates: purchaseRelatedUpdates.length };
}

/**
 * اختبار عملية بيع وتأثيرها على عرض الخزينة
 */
async function testSaleImpactOnCashboxDisplay() {
  console.log('\n💵 اختبار تأثير عملية البيع على عرض الخزينة...');
  
  // إعادة تعيين العدادات
  const initialCounts = {
    cashboxEvents: cashboxUpdateEvents.length,
    profitUpdates: profitDisplayUpdates.length
  };
  
  // محاكاة حدث direct-cashbox-update للبيع
  const directCashboxEvent = new CustomEvent('direct-cashbox-update', {
    detail: {
      transaction_type: 'sale',
      amount: 1500,
      profit: 300,
      current_balance: 10500,
      profit_total: 5300,
      test: true,
      timestamp: new Date().toISOString()
    }
  });
  window.dispatchEvent(directCashboxEvent);
  
  // اختبار استدعاء window.loadCashboxInfo للبيع
  if (window.loadCashboxInfo) {
    console.log('اختبار استدعاء window.loadCashboxInfo للبيع...');
    window.loadCashboxInfo('sale');
  }
  
  // انتظار قصير
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // فحص النتائج
  const finalCounts = {
    cashboxEvents: cashboxUpdateEvents.length - initialCounts.cashboxEvents,
    profitUpdates: profitDisplayUpdates.length - initialCounts.profitUpdates
  };
  
  console.log(`📊 نتائج اختبار تأثير البيع على عرض الخزينة:`);
  console.log(`   - أحداث تحديث الخزينة: ${finalCounts.cashboxEvents}`);
  console.log(`   - تحديثات عرض الأرباح: ${finalCounts.profitUpdates}`);
  
  // التحقق من أن عمليات البيع تحدث عرض الأرباح
  const saleRelatedUpdates = profitDisplayUpdates.slice(-finalCounts.profitUpdates)
    .filter(update => update.transaction_type === 'sale');
  
  console.log(`   - تحديثات مرتبطة بالبيع: ${saleRelatedUpdates.length}`);
  
  const success = saleRelatedUpdates.length > 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  return { success, ...finalCounts, saleRelatedUpdates: saleRelatedUpdates.length };
}

/**
 * تشغيل اختبار شامل لإصلاح عرض الخزينة
 */
async function runCashboxVisualFixTest() {
  console.log('🚀 بدء اختبار إصلاح الخطأ البصري في عرض الخزينة...');
  console.log('='.repeat(60));
  
  // إعداد المراقبة
  setupCashboxMonitoring();
  
  // انتظار قصير للتأكد من الإعداد
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // اختبار تأثير الشراء
  const purchaseResult = await testPurchaseImpactOnCashboxDisplay();
  
  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // اختبار تأثير البيع
  const saleResult = await testSaleImpactOnCashboxDisplay();
  
  // النتائج النهائية
  console.log('\n🏁 النتائج النهائية لاختبار عرض الخزينة:');
  console.log('='.repeat(60));
  console.log(`${purchaseResult.success ? '✅' : '❌'} اختبار تأثير الشراء: ${purchaseResult.success ? 'نجح' : 'فشل'}`);
  console.log(`${saleResult.success ? '✅' : '❌'} اختبار تأثير البيع: ${saleResult.success ? 'نجح' : 'فشل'}`);
  
  const overallSuccess = purchaseResult.success && saleResult.success;
  console.log('='.repeat(60));
  console.log(`🎯 النتيجة الإجمالية: ${overallSuccess ? '✅ إصلاح عرض الخزينة مكتمل' : '❌ يحتاج مراجعة'}`);
  
  if (overallSuccess) {
    console.log('🎉 تم إصلاح الخطأ البصري في عرض الخزينة بنجاح!');
    console.log('✅ عمليات الشراء لا تؤثر على عرض الأرباح في الخزينة');
    console.log('✅ عمليات البيع تحدث عرض الأرباح بشكل صحيح');
  } else {
    console.log('⚠️ هناك مشاكل في إصلاح عرض الخزينة تحتاج إلى مراجعة.');
    
    if (!purchaseResult.success) {
      console.log('❌ مشكلة: عمليات الشراء لا تزال تؤثر على عرض الأرباح في الخزينة');
      console.log(`   - تحديثات مرتبطة بالشراء: ${purchaseResult.purchaseRelatedUpdates}`);
    }
    
    if (!saleResult.success) {
      console.log('❌ مشكلة: عمليات البيع لا تحدث عرض الأرباح في الخزينة');
      console.log(`   - تحديثات مرتبطة بالبيع: ${saleResult.saleRelatedUpdates}`);
    }
  }
  
  // إحصائيات شاملة
  console.log('\n📊 إحصائيات شاملة:');
  console.log(`   - إجمالي أحداث تحديث الخزينة: ${cashboxUpdateEvents.length}`);
  console.log(`   - إجمالي تحديثات عرض الأرباح: ${profitDisplayUpdates.length}`);
  
  return {
    overallSuccess,
    purchaseResult,
    saleResult,
    statistics: {
      totalCashboxEvents: cashboxUpdateEvents.length,
      totalProfitUpdates: profitDisplayUpdates.length
    }
  };
}

/**
 * إعادة تعيين الاختبار
 */
function resetCashboxTest() {
  cashboxUpdateEvents = [];
  profitDisplayUpdates = [];
  console.log('🔄 تم إعادة تعيين اختبار عرض الخزينة');
}

// تصدير الدوال للاستخدام الخارجي
window.cashboxVisualFixTest = {
  runCashboxVisualFixTest,
  testPurchaseImpactOnCashboxDisplay,
  testSaleImpactOnCashboxDisplay,
  setupCashboxMonitoring,
  resetCashboxTest,
  getEvents: () => ({ cashboxUpdateEvents, profitDisplayUpdates })
};

console.log('✅ تم تحميل اختبار إصلاح عرض الخزينة');
console.log('📝 للتشغيل: cashboxVisualFixTest.runCashboxVisualFixTest()');
