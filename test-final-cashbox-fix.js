/**
 * اختبار نهائي لإصلاح مشكلة عرض الخزينة
 * 
 * هذا الاختبار يتحقق من أن عمليات الشراء لا تؤثر على عرض الأرباح في صفحة الخزينة
 */

// متغيرات لتتبع استدعاءات window.loadCashboxInfo
let loadCashboxInfoCalls = [];
let updateCashboxDirectlyCalls = [];

/**
 * إعداد مراقبة استدعاءات تحديث الخزينة
 */
function setupFinalCashboxMonitoring() {
  console.log('🔍 إعداد مراقبة نهائية لتحديثات الخزينة...');
  
  // إعادة تعيين المصفوفات
  loadCashboxInfoCalls = [];
  updateCashboxDirectlyCalls = [];
  
  // مراقبة استدعاءات window.loadCashboxInfo
  if (window.loadCashboxInfo) {
    const originalLoadCashboxInfo = window.loadCashboxInfo;
    window.loadCashboxInfo = function(transactionType) {
      const callInfo = {
        timestamp: new Date().toISOString(),
        transactionType: transactionType,
        ignored: transactionType === 'purchase'
      };
      
      loadCashboxInfoCalls.push(callInfo);
      console.log(`📊 استدعاء window.loadCashboxInfo:`, callInfo);
      
      // استدعاء الدالة الأصلية
      return originalLoadCashboxInfo.call(this, transactionType);
    };
  }
  
  // مراقبة استدعاءات updateCashboxDirectly (إذا كانت متوفرة)
  if (window.updateCashboxDirectly) {
    const originalUpdateCashboxDirectly = window.updateCashboxDirectly;
    window.updateCashboxDirectly = function() {
      const callInfo = {
        timestamp: new Date().toISOString(),
        source: 'direct'
      };
      
      updateCashboxDirectlyCalls.push(callInfo);
      console.log(`🔄 استدعاء updateCashboxDirectly:`, callInfo);
      
      // استدعاء الدالة الأصلية
      return originalUpdateCashboxDirectly.call(this);
    };
  }
  
  console.log('✅ تم إعداد مراقبة تحديثات الخزينة النهائية');
}

/**
 * اختبار تأثير عملية شراء على عرض الخزينة
 */
async function testFinalPurchaseImpact() {
  console.log('\n🛒 اختبار نهائي: تأثير عملية الشراء على عرض الخزينة...');
  
  // إعادة تعيين العدادات
  const initialCounts = {
    loadCashboxInfo: loadCashboxInfoCalls.length,
    updateCashboxDirectly: updateCashboxDirectlyCalls.length
  };
  
  // 1. محاكاة حدث TRANSACTION_ADDED للشراء
  if (window.api && window.api.emit) {
    try {
      console.log('إرسال حدث TRANSACTION_ADDED للشراء...');
      window.api.emit('transaction-added', {
        transaction_type: 'purchase',
        amount: 1000,
        test: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }
  
  // 2. محاكاة حدث CASHBOX_UPDATED للشراء
  if (window.api && window.api.emit) {
    try {
      console.log('إرسال حدث CASHBOX_UPDATED للشراء...');
      window.api.emit('cashbox-updated', {
        transaction_type: 'purchase',
        amount: 1000,
        current_balance: 9000,
        profit_total: 5000,
        test: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }
  
  // 3. محاكاة حدث direct-update للشراء
  if (window.api && window.api.emit) {
    try {
      console.log('إرسال حدث direct-update للشراء...');
      window.api.emit('direct-update', {
        transaction_type: 'purchase',
        amount: 1000,
        test: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }
  
  // 4. اختبار استدعاء refreshCashboxInfo مباشرة
  if (window.eventListeners && window.eventListeners.refreshCashboxInfo) {
    console.log('اختبار استدعاء refreshCashboxInfo مباشرة للشراء...');
    window.eventListeners.refreshCashboxInfo('purchase');
  }
  
  // انتظار قصير للسماح بمعالجة الأحداث
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // فحص النتائج
  const finalCounts = {
    loadCashboxInfo: loadCashboxInfoCalls.length - initialCounts.loadCashboxInfo,
    updateCashboxDirectly: updateCashboxDirectlyCalls.length - initialCounts.updateCashboxDirectly
  };
  
  console.log(`📊 نتائج اختبار تأثير الشراء النهائي:`);
  console.log(`   - استدعاءات window.loadCashboxInfo: ${finalCounts.loadCashboxInfo}`);
  console.log(`   - استدعاءات updateCashboxDirectly: ${finalCounts.updateCashboxDirectly}`);
  
  // التحقق من أن عمليات الشراء تم تجاهلها
  const purchaseRelatedCalls = loadCashboxInfoCalls.slice(-finalCounts.loadCashboxInfo)
    .filter(call => call.transactionType === 'purchase' && !call.ignored);
  
  console.log(`   - استدعاءات غير مُتجاهلة للشراء: ${purchaseRelatedCalls.length}`);
  
  const success = purchaseRelatedCalls.length === 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  if (!success) {
    console.log('❌ تفاصيل الاستدعاءات غير المُتجاهلة:');
    purchaseRelatedCalls.forEach((call, index) => {
      console.log(`   ${index + 1}. ${call.timestamp} - نوع: ${call.transactionType}`);
    });
  }
  
  return { success, ...finalCounts, purchaseRelatedCalls: purchaseRelatedCalls.length };
}

/**
 * اختبار تأثير عملية بيع على عرض الخزينة
 */
async function testFinalSaleImpact() {
  console.log('\n💵 اختبار نهائي: تأثير عملية البيع على عرض الخزينة...');
  
  // إعادة تعيين العدادات
  const initialCounts = {
    loadCashboxInfo: loadCashboxInfoCalls.length,
    updateCashboxDirectly: updateCashboxDirectlyCalls.length
  };
  
  // محاكاة حدث CASHBOX_UPDATED للبيع
  if (window.api && window.api.emit) {
    try {
      console.log('إرسال حدث CASHBOX_UPDATED للبيع...');
      window.api.emit('cashbox-updated', {
        transaction_type: 'sale',
        amount: 1500,
        profit: 300,
        current_balance: 10500,
        profit_total: 5300,
        test: true,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.log('لا يمكن محاكاة أحداث API:', error.message);
    }
  }
  
  // اختبار استدعاء refreshCashboxInfo للبيع
  if (window.eventListeners && window.eventListeners.refreshCashboxInfo) {
    console.log('اختبار استدعاء refreshCashboxInfo للبيع...');
    window.eventListeners.refreshCashboxInfo('sale');
  }
  
  // انتظار قصير
  await new Promise(resolve => setTimeout(resolve, 300));
  
  // فحص النتائج
  const finalCounts = {
    loadCashboxInfo: loadCashboxInfoCalls.length - initialCounts.loadCashboxInfo,
    updateCashboxDirectly: updateCashboxDirectlyCalls.length - initialCounts.updateCashboxDirectly
  };
  
  console.log(`📊 نتائج اختبار تأثير البيع النهائي:`);
  console.log(`   - استدعاءات window.loadCashboxInfo: ${finalCounts.loadCashboxInfo}`);
  console.log(`   - استدعاءات updateCashboxDirectly: ${finalCounts.updateCashboxDirectly}`);
  
  // التحقق من أن عمليات البيع تم تنفيذها
  const saleRelatedCalls = loadCashboxInfoCalls.slice(-finalCounts.loadCashboxInfo)
    .filter(call => call.transactionType === 'sale' && !call.ignored);
  
  console.log(`   - استدعاءات منفذة للبيع: ${saleRelatedCalls.length}`);
  
  const success = saleRelatedCalls.length > 0;
  console.log(`   - النتيجة: ${success ? '✅ نجح' : '❌ فشل'}`);
  
  return { success, ...finalCounts, saleRelatedCalls: saleRelatedCalls.length };
}

/**
 * تشغيل الاختبار النهائي الشامل
 */
async function runFinalCashboxFixTest() {
  console.log('🚀 بدء الاختبار النهائي لإصلاح عرض الخزينة...');
  console.log('='.repeat(60));
  
  // إعداد المراقبة
  setupFinalCashboxMonitoring();
  
  // انتظار قصير للتأكد من الإعداد
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // اختبار تأثير الشراء
  const purchaseResult = await testFinalPurchaseImpact();
  
  // انتظار بين الاختبارات
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // اختبار تأثير البيع
  const saleResult = await testFinalSaleImpact();
  
  // النتائج النهائية
  console.log('\n🏁 النتائج النهائية لإصلاح عرض الخزينة:');
  console.log('='.repeat(60));
  console.log(`${purchaseResult.success ? '✅' : '❌'} اختبار تأثير الشراء: ${purchaseResult.success ? 'نجح' : 'فشل'}`);
  console.log(`${saleResult.success ? '✅' : '❌'} اختبار تأثير البيع: ${saleResult.success ? 'نجح' : 'فشل'}`);
  
  const overallSuccess = purchaseResult.success && saleResult.success;
  console.log('='.repeat(60));
  console.log(`🎯 النتيجة الإجمالية: ${overallSuccess ? '✅ إصلاح عرض الخزينة مكتمل نهائياً' : '❌ يحتاج مراجعة'}`);
  
  if (overallSuccess) {
    console.log('🎉 تم إصلاح مشكلة عرض الخزينة بنجاح نهائياً!');
    console.log('✅ عمليات الشراء لا تؤثر على عرض الأرباح في الخزينة');
    console.log('✅ عمليات البيع تحدث عرض الأرباح بشكل صحيح');
    console.log('✅ جميع المستمعين والدوال تعمل بشكل صحيح');
  } else {
    console.log('⚠️ هناك مشاكل في إصلاح عرض الخزينة تحتاج إلى مراجعة.');
    
    if (!purchaseResult.success) {
      console.log('❌ مشكلة: عمليات الشراء لا تزال تؤثر على عرض الأرباح في الخزينة');
      console.log(`   - استدعاءات غير مُتجاهلة: ${purchaseResult.purchaseRelatedCalls}`);
    }
    
    if (!saleResult.success) {
      console.log('❌ مشكلة: عمليات البيع لا تحدث عرض الأرباح في الخزينة');
      console.log(`   - استدعاءات منفذة: ${saleResult.saleRelatedCalls}`);
    }
  }
  
  // إحصائيات شاملة
  console.log('\n📊 إحصائيات شاملة:');
  console.log(`   - إجمالي استدعاءات window.loadCashboxInfo: ${loadCashboxInfoCalls.length}`);
  console.log(`   - إجمالي استدعاءات updateCashboxDirectly: ${updateCashboxDirectlyCalls.length}`);
  
  return {
    overallSuccess,
    purchaseResult,
    saleResult,
    statistics: {
      totalLoadCashboxInfoCalls: loadCashboxInfoCalls.length,
      totalUpdateCashboxDirectlyCalls: updateCashboxDirectlyCalls.length
    }
  };
}

/**
 * إعادة تعيين الاختبار النهائي
 */
function resetFinalCashboxTest() {
  loadCashboxInfoCalls = [];
  updateCashboxDirectlyCalls = [];
  console.log('🔄 تم إعادة تعيين الاختبار النهائي لعرض الخزينة');
}

// تصدير الدوال للاستخدام الخارجي
window.finalCashboxFixTest = {
  runFinalCashboxFixTest,
  testFinalPurchaseImpact,
  testFinalSaleImpact,
  setupFinalCashboxMonitoring,
  resetFinalCashboxTest,
  getCalls: () => ({ loadCashboxInfoCalls, updateCashboxDirectlyCalls })
};

console.log('✅ تم تحميل الاختبار النهائي لإصلاح عرض الخزينة');
console.log('📝 للتشغيل: finalCashboxFixTest.runFinalCashboxFixTest()');
