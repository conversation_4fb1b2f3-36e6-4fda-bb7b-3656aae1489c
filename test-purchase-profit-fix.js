/**
 * اختبار شامل لإصلاح مشكلة تأثير عمليات الشراء على عرض الأرباح
 * 
 * هذا الاختبار يتحقق من:
 * 1. عدم تأثير عمليات الشراء على عرض الأرباح في واجهة المستخدم
 * 2. استمرار عمل تحديث الأرباح بشكل صحيح أثناء عمليات البيع
 * 3. عدم إرسال أحداث تحديث الأرباح أثناء عمليات الشراء
 */

console.log('🧪 بدء اختبار إصلاح مشكلة تأثير عمليات الشراء على عرض الأرباح...');

// متغيرات لتتبع الأحداث
let profitUpdateEvents = [];
let purchaseEvents = [];
let saleEvents = [];

/**
 * إعداد مراقبة الأحداث
 */
function setupEventMonitoring() {
  console.log('👁️ إعداد مراقبة الأحداث...');
  
  const eventsToMonitor = [
    'profits-updated',
    'auto-profits-updated',
    'direct-profits-update',
    'financial-profits-updated',
    'real-time-profits-updated',
    'purchase-completed',
    'cashbox-updated-ui',
    'direct-update'
  ];
  
  eventsToMonitor.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      const eventData = {
        type: eventName,
        timestamp: new Date().toISOString(),
        detail: event.detail,
        transaction_type: event.detail?.transaction_type
      };
      
      // تصنيف الأحداث
      if (eventName.includes('profit')) {
        profitUpdateEvents.push(eventData);
        console.log(`💰 حدث ربح مستلم: ${eventName}`, eventData);
      }
      
      if (event.detail?.transaction_type === 'purchase') {
        purchaseEvents.push(eventData);
        console.log(`🛒 حدث شراء مستلم: ${eventName}`, eventData);
      }
      
      if (event.detail?.transaction_type === 'sale') {
        saleEvents.push(eventData);
        console.log(`💵 حدث بيع مستلم: ${eventName}`, eventData);
      }
    });
  });
  
  console.log('✅ تم إعداد مراقبة الأحداث');
}

/**
 * محاكاة عملية شراء
 */
async function simulatePurchaseTransaction() {
  console.log('🛒 محاكاة عملية شراء...');
  
  // إعادة تعيين عدادات الأحداث
  const initialProfitEvents = profitUpdateEvents.length;
  const initialPurchaseEvents = purchaseEvents.length;
  
  // محاكاة حدث شراء
  const purchaseEvent = new CustomEvent('purchase-completed', {
    detail: {
      transaction_type: 'purchase',
      amount: 1000,
      transport_cost: 50,
      total_profit: 5000, // قيمة ثابتة للاختبار
      timestamp: new Date().toISOString(),
      test: true
    }
  });
  
  window.dispatchEvent(purchaseEvent);
  
  // انتظار قصير للسماح بمعالجة الأحداث
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // التحقق من النتائج
  const newProfitEvents = profitUpdateEvents.length - initialProfitEvents;
  const newPurchaseEvents = purchaseEvents.length - initialPurchaseEvents;
  
  console.log(`📊 نتائج اختبار الشراء:`);
  console.log(`   - أحداث الأرباح الجديدة: ${newProfitEvents}`);
  console.log(`   - أحداث الشراء الجديدة: ${newPurchaseEvents}`);
  
  return {
    profitEventsTriggered: newProfitEvents,
    purchaseEventsTriggered: newPurchaseEvents,
    success: newProfitEvents === 0 && newPurchaseEvents >= 1
  };
}

/**
 * محاكاة عملية بيع
 */
async function simulateSaleTransaction() {
  console.log('💵 محاكاة عملية بيع...');
  
  // إعادة تعيين عدادات الأحداث
  const initialProfitEvents = profitUpdateEvents.length;
  const initialSaleEvents = saleEvents.length;
  
  // محاكاة حدث بيع
  const saleEvent = new CustomEvent('auto-profits-updated', {
    detail: {
      transaction_type: 'sale',
      amount: 1500,
      profit: 300,
      total_profit: 5300,
      auto_update: true,
      timestamp: new Date().toISOString(),
      test: true
    }
  });
  
  window.dispatchEvent(saleEvent);
  
  // انتظار قصير للسماح بمعالجة الأحداث
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // التحقق من النتائج
  const newProfitEvents = profitUpdateEvents.length - initialProfitEvents;
  const newSaleEvents = saleEvents.length - initialSaleEvents;
  
  console.log(`📊 نتائج اختبار البيع:`);
  console.log(`   - أحداث الأرباح الجديدة: ${newProfitEvents}`);
  console.log(`   - أحداث البيع الجديدة: ${newSaleEvents}`);
  
  return {
    profitEventsTriggered: newProfitEvents,
    saleEventsTriggered: newSaleEvents,
    success: newProfitEvents >= 1 && newSaleEvents >= 1
  };
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  console.log('🚀 بدء تشغيل جميع الاختبارات...');
  
  const results = {
    setupSuccess: false,
    purchaseTest: null,
    saleTest: null,
    overallSuccess: false
  };
  
  try {
    // إعداد مراقبة الأحداث
    setupEventMonitoring();
    results.setupSuccess = true;
    
    // انتظار قصير للتأكد من إعداد المراقبة
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // اختبار عملية الشراء
    console.log('\n--- اختبار عملية الشراء ---');
    results.purchaseTest = await simulatePurchaseTransaction();
    
    // انتظار بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // اختبار عملية البيع
    console.log('\n--- اختبار عملية البيع ---');
    results.saleTest = await simulateSaleTransaction();
    
    // تقييم النتائج الإجمالية
    results.overallSuccess = 
      results.setupSuccess &&
      results.purchaseTest?.success &&
      results.saleTest?.success;
    
    // عرض النتائج النهائية
    console.log('\n🏁 النتائج النهائية:');
    console.log('=====================================');
    console.log(`✅ إعداد المراقبة: ${results.setupSuccess ? 'نجح' : 'فشل'}`);
    console.log(`${results.purchaseTest?.success ? '✅' : '❌'} اختبار الشراء: ${results.purchaseTest?.success ? 'نجح' : 'فشل'}`);
    console.log(`${results.saleTest?.success ? '✅' : '❌'} اختبار البيع: ${results.saleTest?.success ? 'نجح' : 'فشل'}`);
    console.log('=====================================');
    console.log(`🎯 النتيجة الإجمالية: ${results.overallSuccess ? '✅ نجح الإصلاح' : '❌ يحتاج مراجعة'}`);
    
    if (results.overallSuccess) {
      console.log('🎉 تم إصلاح مشكلة تأثير عمليات الشراء على عرض الأرباح بنجاح!');
      console.log('✅ عمليات الشراء لا تؤثر على عرض الأرباح');
      console.log('✅ عمليات البيع تحدث الأرباح بشكل صحيح');
    } else {
      console.log('⚠️ هناك مشاكل تحتاج إلى مراجعة في الإصلاح.');
      
      if (!results.purchaseTest?.success) {
        console.log('❌ مشكلة: عمليات الشراء لا تزال تؤثر على عرض الأرباح');
      }
      
      if (!results.saleTest?.success) {
        console.log('❌ مشكلة: عمليات البيع لا تحدث الأرباح بشكل صحيح');
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ أثناء تشغيل الاختبارات:', error);
    results.overallSuccess = false;
  }
  
  return results;
}

/**
 * إعادة تعيين الاختبارات
 */
function resetTests() {
  profitUpdateEvents = [];
  purchaseEvents = [];
  saleEvents = [];
  console.log('🔄 تم إعادة تعيين الاختبارات');
}

// تصدير الدوال للاستخدام الخارجي
window.purchaseProfitTest = {
  runAllTests,
  simulatePurchaseTransaction,
  simulateSaleTransaction,
  setupEventMonitoring,
  resetTests,
  getEvents: () => ({ profitUpdateEvents, purchaseEvents, saleEvents })
};

console.log('📝 تم تحميل اختبار إصلاح مشكلة الأرباح.');
console.log('💡 استخدم window.purchaseProfitTest.runAllTests() لتشغيل الاختبارات.');
console.log('🔄 استخدم window.purchaseProfitTest.resetTests() لإعادة تعيين الاختبارات.');
